import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import axios from 'axios';

const Signup = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');

  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    try {
      const payload = { name, email, password };
      await axios.post('http://localhost:4000/signup', payload);
      alert('Account created successfully!');
      navigate('/login');
    } catch (err) {
      setError('Failed to create an account');
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-teal-100 to-white">
      <div className="max-w-md w-full bg-white p-8 rounded-md shadow-md relative z-10">
        <div className="flex flex-col items-center mb-6">
          <img src="/bookstore-logo.png" alt="Book Store Logo" className="w-20 h-20 mb-2" />
          <h2 className="text-3xl font-extrabold text-gray-900">Create Your Book Store Account</h2>
          <p className="text-gray-500 mt-2">Sign up to explore and buy your favorite books!</p>
        </div>
        <form className="space-y-6" onSubmit={handleSubmit}>
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              Name
            </label>
            <input
              id="name"
              name="name"
              type="text"
              autoComplete="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="mt-1 p-2 block w-full border border-gray-300 rounded-md focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
              placeholder="Your Name"
              required
            />
          </div>
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email address
            </label>
            <input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mt-1 p-2 block w-full border border-gray-300 rounded-md focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
              placeholder="Email address"
              required
            />
          </div>
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700">
              Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              autoComplete="new-password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="mt-1 p-2 block w-full border border-gray-300 rounded-md focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
              placeholder="Password"
              required
            />
          </div>
          {error && <div className="text-red-500 text-sm">{error}</div>}
          <div>
            <button
              type="submit"
              className="w-full bg-teal-600 hover:bg-teal-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:ring focus:border-teal-300 transition-all duration-300"
            >
              Sign Up
            </button>
          </div>
        </form>
        <p className="text-sm text-gray-600 mt-4 text-center">
          Already have an account?{' '}
          <Link
            to="/login"
            className="text-teal-500 hover:underline focus:outline-none focus:ring focus:border-teal-300 transition-all duration-300"
          >
            Login
          </Link>
        </p>
        <div className="absolute h-full w-full bg-teal-200 opacity-20 top-0 left-0 rounded-md -z-10"></div>
      </div>
    </div>
  );
};

export default Signup;
