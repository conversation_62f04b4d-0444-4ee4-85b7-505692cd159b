import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import './BookStore.css';

const Products = () => {
  const [books, setBooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    fetchBooks();
  }, []);

  const fetchBooks = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:4000/books');
      setBooks(response.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching books:', err);
      setError('Failed to load books. Please try again later.');
      // Fallback to sample data if API fails
      setBooks([
        {
          _id: '1',
          title: "The Great Gatsby",
          author: "<PERSON><PERSON>",
          price: "299",
          description: "A classic American novel set in the Jazz Age.",
          itemImage: null
        },
        {
          _id: '2',
          title: "To Kill a Mockingbird",
          author: "Harper Lee",
          price: "349",
          description: "A gripping tale of racial injustice and childhood innocence.",
          itemImage: null
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const getImageUrl = (itemImage) => {
    if (!itemImage) {
      return '/default_cover.svg';
    }
    // If itemImage is already a full URL, use it as is
    if (itemImage.startsWith('http')) {
      return itemImage;
    }
    // Otherwise, construct the URL with the backend server
    return `http://localhost:4000/${itemImage}`;
  };

  const addToCart = (book) => {
    try {
      // Get existing cart from localStorage
      const existingCart = JSON.parse(localStorage.getItem('cart') || '[]');

      // Check if book already exists in cart
      const existingItemIndex = existingCart.findIndex(item => item._id === book._id);

      if (existingItemIndex > -1) {
        // If book exists, increase quantity
        existingCart[existingItemIndex].quantity += 1;
      } else {
        // If book doesn't exist, add new item
        existingCart.push({
          ...book,
          quantity: 1,
          addedAt: new Date().toISOString()
        });
      }

      // Save updated cart to localStorage
      localStorage.setItem('cart', JSON.stringify(existingCart));

      // Show success message
      alert(`"${book.title}" has been added to your cart!`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      alert('Failed to add item to cart. Please try again.');
    }
  };

  if (loading) {
    return (
      <div className="container mt-5">
        <div className="text-center">
          <div className="loading"></div>
          <p>Loading books...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mt-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2 className="text-primary">Our Book Collection</h2>
        <div>
          <button
            className="btn btn-outline-primary me-2"
            onClick={() => navigate('/mycart')}
          >
            My Cart
          </button>
          <button
            className="btn btn-outline-secondary"
            onClick={() => navigate('/myorders-new')}
          >
            My Orders
          </button>
        </div>
      </div>
      {error && (
        <div className="alert alert-warning text-center mb-4">
          {error}
        </div>
      )}
      <div className="row">
        {books.map(book => (
          <div key={book._id || book.id} className="col-md-6 col-lg-4 mb-4">
            <div className="bookstore-card card h-100">
              <img
                src={getImageUrl(book.itemImage)}
                className="card-img-top book-cover"
                alt={book.title}
                onError={(e) => {
                  e.target.src = '/default_cover.svg';
                }}
              />
              <div className="card-body d-flex flex-column">
                <h5 className="card-title text-primary">{book.title}</h5>
                <p className="card-text text-secondary">by {book.author}</p>
                <p className="card-text flex-grow-1">{book.description}</p>
                <div className="mt-auto">
                  <p className="h5 text-success mb-2">₹{book.price}</p>
                  <button
                    className="btn btn-primary w-100"
                    onClick={() => addToCart(book)}
                  >
                    Add to Cart
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Products;
