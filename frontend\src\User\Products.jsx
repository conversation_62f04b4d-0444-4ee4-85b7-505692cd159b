import React, { useState, useEffect } from 'react';
import './BookStore.css';

const Products = () => {
  const [books, setBooks] = useState([]);
  const [loading, setLoading] = useState(true);

  // Sample book data for demonstration
  const sampleBooks = [
    {
      id: 1,
      title: "The Great Gatsby",
      author: "<PERSON><PERSON>",
      price: 299,
      description: "A classic American novel set in the Jazz Age.",
      cover: "/default_cover.svg"
    },
    {
      id: 2,
      title: "To Kill a Mockingbird",
      author: "Harper Lee",
      price: 349,
      description: "A gripping tale of racial injustice and childhood innocence.",
      cover: "/default_cover.svg"
    },
    {
      id: 3,
      title: "1984",
      author: "<PERSON>",
      price: 399,
      description: "A dystopian social science fiction novel.",
      cover: "/default_cover.svg"
    },
    {
      id: 4,
      title: "Pride and Prejudice",
      author: "Jane Austen",
      price: 279,
      description: "A romantic novel of manners.",
      cover: "/default_cover.svg"
    }
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setBooks(sampleBooks);
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return (
      <div className="container mt-5">
        <div className="text-center">
          <div className="loading"></div>
          <p>Loading books...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mt-4">
      <h2 className="text-center mb-4 text-primary">Our Book Collection</h2>
      <div className="row">
        {books.map(book => (
          <div key={book.id} className="col-md-6 col-lg-4 mb-4">
            <div className="bookstore-card card h-100">
              <img 
                src={book.cover} 
                className="card-img-top book-cover" 
                alt={book.title}
                onError={(e) => {
                  e.target.src = '/default_cover.svg';
                }}
              />
              <div className="card-body d-flex flex-column">
                <h5 className="card-title text-primary">{book.title}</h5>
                <p className="card-text text-secondary">by {book.author}</p>
                <p className="card-text flex-grow-1">{book.description}</p>
                <div className="mt-auto">
                  <p className="h5 text-success mb-2">₹{book.price}</p>
                  <button className="btn btn-primary w-100">
                    Add to Cart
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Products;
