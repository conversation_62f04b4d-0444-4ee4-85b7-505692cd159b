import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { getImageUrl, handleImageError } from '../utils/imageUtils';
import './BookStore.css';

const MyOrdersNew = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      
      if (!user.id) {
        setError('Please login to view your orders');
        setLoading(false);
        return;
      }

      const response = await axios.get(`http://localhost:4000/getorders/${user.id}`);
      setOrders(response.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching orders:', err);
      setError('Failed to load orders. Please try again later.');
      // For demo purposes, show sample orders if API fails
      setOrders([
        {
          _id: '1',
          booktitle: 'The Great Gatsby',
          bookauthor: 'F. Scott Fitzgerald',
          totalamount: '299',
          BookingDate: '12/25/2024',
          Delivery: '1/1/2025',
          itemImage: null,
          description: 'A classic American novel',
          city: 'Mumbai',
          state: 'Maharashtra'
        },
        {
          _id: '2',
          booktitle: 'To Kill a Mockingbird',
          bookauthor: 'Harper Lee',
          totalamount: '349',
          BookingDate: '12/20/2024',
          Delivery: '12/27/2024',
          itemImage: null,
          description: 'A gripping tale of racial injustice',
          city: 'Delhi',
          state: 'Delhi'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };



  const getOrderStatus = (bookingDate, deliveryDate) => {
    const today = new Date();
    const booking = new Date(bookingDate);
    const delivery = new Date(deliveryDate);
    
    if (today < booking) {
      return { status: 'Pending', color: 'warning' };
    } else if (today >= booking && today < delivery) {
      return { status: 'Processing', color: 'info' };
    } else {
      return { status: 'Delivered', color: 'success' };
    }
  };

  if (loading) {
    return (
      <div className="container mt-5">
        <div className="text-center">
          <div className="loading"></div>
          <p>Loading your orders...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mt-4">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2 className="text-primary">My Orders</h2>
        <button 
          className="btn btn-outline-secondary"
          onClick={() => navigate('/books')}
        >
          Continue Shopping
        </button>
      </div>

      {error && (
        <div className="alert alert-warning text-center mb-4">
          {error}
        </div>
      )}

      {orders.length === 0 ? (
        <div className="text-center py-5">
          <h4 className="text-muted">No orders found</h4>
          <p className="text-muted">You haven't placed any orders yet!</p>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/books')}
          >
            Start Shopping
          </button>
        </div>
      ) : (
        <div className="row">
          {orders.map(order => {
            const orderStatus = getOrderStatus(order.BookingDate, order.Delivery);
            return (
              <div key={order._id} className="col-12 mb-4">
                <div className="card">
                  <div className="card-header d-flex justify-content-between align-items-center">
                    <h6 className="mb-0">Order #{order._id}</h6>
                    <span className={`badge badge-${orderStatus.color}`}>
                      {orderStatus.status}
                    </span>
                  </div>
                  <div className="card-body">
                    <div className="row">
                      <div className="col-md-2">
                        <img 
                          src={getImageUrl(order.itemImage)} 
                          className="img-fluid rounded"
                          alt={order.booktitle}
                          style={{ maxHeight: '120px', objectFit: 'cover' }}
                          onError={handleImageError}
                        />
                      </div>
                      <div className="col-md-6">
                        <h5 className="text-primary">{order.booktitle}</h5>
                        <p className="text-muted mb-1">by {order.bookauthor}</p>
                        <p className="text-muted mb-2">{order.description}</p>
                        <div className="row">
                          <div className="col-sm-6">
                            <small className="text-muted">
                              <strong>Delivery Address:</strong><br />
                              {order.city}, {order.state}
                            </small>
                          </div>
                          <div className="col-sm-6">
                            <small className="text-muted">
                              <strong>Genre:</strong> {order.bookgenre || 'Not specified'}
                            </small>
                          </div>
                        </div>
                      </div>
                      <div className="col-md-4">
                        <div className="text-right">
                          <h5 className="text-success mb-3">₹{order.totalamount}</h5>
                          <div className="mb-2">
                            <small className="text-muted">
                              <strong>Order Date:</strong><br />
                              {order.BookingDate}
                            </small>
                          </div>
                          <div className="mb-2">
                            <small className="text-muted">
                              <strong>Expected Delivery:</strong><br />
                              {order.Delivery}
                            </small>
                          </div>
                          <div className="mt-3">
                            <button 
                              className="btn btn-outline-primary btn-sm mr-2"
                              onClick={() => navigate(`/orderitem/${order._id}`)}
                            >
                              View Details
                            </button>
                            {orderStatus.status === 'Delivered' && (
                              <button 
                                className="btn btn-outline-secondary btn-sm"
                                onClick={() => alert('Review functionality coming soon!')}
                              >
                                Write Review
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {orders.length > 0 && (
        <div className="text-center mt-4">
          <p className="text-muted">
            Showing {orders.length} order{orders.length !== 1 ? 's' : ''}
          </p>
        </div>
      )}
    </div>
  );
};

export default MyOrdersNew;
