<svg width="300" height="400" viewBox="0 0 300 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="300" height="400" fill="url(#gradient)"/>
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#14b8a6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f766e;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Book Icon -->
  <g transform="translate(150, 150)">
    <rect x="-40" y="-50" width="80" height="100" fill="white" fill-opacity="0.2" rx="4"/>
    <rect x="-35" y="-45" width="70" height="90" fill="white" fill-opacity="0.1" rx="2"/>
    <line x1="-30" y1="-30" x2="30" y2="-30" stroke="white" stroke-width="2" stroke-opacity="0.3"/>
    <line x1="-30" y1="-15" x2="30" y2="-15" stroke="white" stroke-width="2" stroke-opacity="0.3"/>
    <line x1="-30" y1="0" x2="30" y2="0" stroke="white" stroke-width="2" stroke-opacity="0.3"/>
    <line x1="-30" y1="15" x2="20" y2="15" stroke="white" stroke-width="2" stroke-opacity="0.3"/>
  </g>
  
  <!-- Text -->
  <text x="150" y="280" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">
    No Image
  </text>
  <text x="150" y="305" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="14" opacity="0.8">
    Available
  </text>
</svg>
